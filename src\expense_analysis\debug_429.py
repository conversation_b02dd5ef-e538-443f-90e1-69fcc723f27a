#!/usr/bin/env python3
"""
429错误专门诊断工具
深度分析429错误的真实原因
"""

import requests
import time
import json
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

class Error429Debugger:
    def __init__(self):
        self.api_url = "http://ai.ai.iot.chinamobile.com/imaas/v1/chat/completions"
        self.api_key = "sk-bCzKwiWgIHHKhTDxxi8GcVT46XKcXz7aqqaP2aXOVLYjhbZZ"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
    def create_minimal_payload(self):
        """创建最小的测试负载"""
        return {
            "model": "Qwen3-235B-A22B",
            "messages": [
                {
                    "role": "user",
                    "content": "请回答：1+1等于几？"
                }
            ],
            "temperature": 0.1,
            "max_tokens": 10
        }
    
    def single_request_debug(self, request_id: int, delay: float = 0):
        """发送单个请求并详细记录"""
        if delay > 0:
            time.sleep(delay)
            
        start_time = time.time()
        thread_id = threading.current_thread().ident
        
        print(f"🚀 请求{request_id} (线程{thread_id}) 开始...")
        
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=self.create_minimal_payload(),
                timeout=30
            )
            
            duration = time.time() - start_time
            
            print(f"✅ 请求{request_id} 成功: {response.status_code} (用时: {duration:.2f}s)")
            
            # 检查响应头中的限制信息
            rate_limit_headers = {}
            for header, value in response.headers.items():
                if any(keyword in header.lower() for keyword in ['rate', 'limit', 'quota', 'remaining', 'reset']):
                    rate_limit_headers[header] = value
            
            if rate_limit_headers:
                print(f"📊 请求{request_id} 限制信息: {rate_limit_headers}")
            
            return {
                'request_id': request_id,
                'thread_id': thread_id,
                'success': True,
                'status_code': response.status_code,
                'duration': duration,
                'headers': dict(response.headers),
                'rate_limit_headers': rate_limit_headers
            }
            
        except requests.exceptions.HTTPError as e:
            duration = time.time() - start_time
            print(f"❌ 请求{request_id} HTTP错误: {e}")
            print(f"   状态码: {e.response.status_code}")
            print(f"   响应头: {dict(e.response.headers)}")
            print(f"   响应内容: {e.response.text[:200]}...")
            
            return {
                'request_id': request_id,
                'thread_id': thread_id,
                'success': False,
                'status_code': e.response.status_code,
                'duration': duration,
                'error': str(e),
                'response_text': e.response.text,
                'headers': dict(e.response.headers)
            }
            
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ 请求{request_id} 其他错误: {e}")
            
            return {
                'request_id': request_id,
                'thread_id': thread_id,
                'success': False,
                'status_code': 0,
                'duration': duration,
                'error': str(e)
            }
    
    def test_sequential_with_delays(self):
        """测试不同延迟的顺序请求"""
        print("🔍 测试1: 顺序请求，不同延迟")
        print("=" * 50)
        
        delays = [0, 0.5, 1.0, 2.0, 5.0]
        
        for delay in delays:
            print(f"\n⏱️ 测试延迟: {delay}秒")
            results = []
            
            for i in range(3):
                result = self.single_request_debug(i + 1, delay)
                results.append(result)
            
            success_count = sum(1 for r in results if r['success'])
            print(f"📊 延迟{delay}s结果: {success_count}/3 成功")
    
    def test_concurrent_load(self):
        """测试并发负载"""
        print("\n🔍 测试2: 并发负载测试")
        print("=" * 50)
        
        thread_counts = [1, 2, 4, 8]
        
        for threads in thread_counts:
            print(f"\n🚀 测试{threads}个并发线程")
            
            with ThreadPoolExecutor(max_workers=threads) as executor:
                futures = [executor.submit(self.single_request_debug, i + 1, 0) 
                          for i in range(threads * 2)]  # 每个线程2个请求
                
                results = [future.result() for future in as_completed(futures)]
            
            success_count = sum(1 for r in results if r['success'])
            error_429_count = sum(1 for r in results if r.get('status_code') == 429)
            
            print(f"📊 {threads}线程结果: {success_count}/{len(results)} 成功, {error_429_count} 个429错误")
    
    def test_api_response_details(self):
        """测试API响应的详细信息"""
        print("\n🔍 测试3: API响应详细分析")
        print("=" * 50)
        
        result = self.single_request_debug(1, 0)
        
        if result['success']:
            print("✅ API正常工作")
            if 'rate_limit_headers' in result and result['rate_limit_headers']:
                print(f"📊 发现限制头: {result['rate_limit_headers']}")
            else:
                print("⚠️ 未发现明显的限制头信息")
        else:
            print("❌ API存在问题")
            if result.get('status_code') == 429:
                print("🚨 确认是429错误")
                if 'response_text' in result:
                    print(f"📝 错误详情: {result['response_text']}")
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🩺 429错误深度诊断开始")
        print(f"🎯 目标API: {self.api_url}")
        print(f"⏰ 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 测试1: 顺序请求
        self.test_sequential_with_delays()
        
        # 测试2: 并发负载
        self.test_concurrent_load()
        
        # 测试3: 详细响应分析
        self.test_api_response_details()
        
        print("\n🎉 诊断完成!")

if __name__ == "__main__":
    debugger = Error429Debugger()
    debugger.run_full_diagnosis()
