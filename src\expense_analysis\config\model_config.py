"""
费用分析专用模型配置
独立于外部agent项目的配置
"""

# 费用分析专用LLM配置
EXPENSE_ANALYSIS_LLM_CONFIG = {
    # 模型选择 (可以随时切换)
    "model_name": "Qwen2.5-72B-Instruct",  # 默认使用您当前的模型
    
    # 备选模型 (根据您的实际模型名称配置)
    "alternative_models": {
        "fastest": "Qwen2.5-7B-Instruct-AWQ",      # 最快速度 (如果有的话)
        "balanced": "Qwen2.5-72B-Instruct",    # 平衡选择 (如果有的话)  # 高准确性 (如果有的话)
        "current": "Qwen3-235B-A22B"             # 您当前使用的模型
    },
    
    # 模型服务配置 (使用相同的服务地址)
    "api_url": "http://ai.ai.iot.chinamobile.com/imaas/v1/chat/completions",
    "api_key": "sk-bCzKwiWgIHHKhTDxxi8GcVT46XKcXz7aqqaP2aXOVLYjhbZZ",  # 可以使用相同的key
    
    # 分类任务优化参数
    "temperature": 0.1,      # 低温度，提高一致性
    "max_tokens": 2000,      # 适中的token数量
    "timeout": 300,          # 5分钟超时
    
    # 性能优化
    "preserve_think_tags": False,   # 启用思考标签，提高准确性
    "stream": False,               # 禁用流式输出
}

# 模型性能对比 (您的3个模型)
MODEL_PERFORMANCE = {
    "Qwen2.5-7B-Instruct-AWQ": {
        "speed": "⭐⭐⭐⭐⭐",
        "accuracy": "⭐⭐⭐⭐",
        "resource": "⭐⭐⭐⭐⭐",
        "description": "最快速度，适合大批量处理",
        "estimated_speed_multiplier": 8
    },

    "Qwen2.5-72B-Instruct": {
        "speed": "⭐⭐⭐",
        "accuracy": "⭐⭐⭐⭐⭐",
        "resource": "⭐⭐",
        "description": "平衡选择，高准确性",
        "estimated_speed_multiplier": 3
    },

    "Qwen3-235B-A22B": {
        "speed": "⭐",
        "accuracy": "⭐⭐⭐⭐⭐",
        "resource": "⭐",
        "description": "最高准确性，速度较慢",
        "estimated_speed_multiplier": 1
    }
}

def get_model_config(model_choice="balanced"):
    """
    获取模型配置
    
    Args:
        model_choice: 模型选择 ("fastest", "balanced", "accurate", "current")
    
    Returns:
        dict: 模型配置
    """
    config = EXPENSE_ANALYSIS_LLM_CONFIG.copy()
    
    if model_choice in config["alternative_models"]:
        config["model_name"] = config["alternative_models"][model_choice]
    
    return config

def print_model_comparison():
    """打印模型对比"""
    print("🤖 您的3个模型对比")
    print("=" * 80)
    print(f"{'模型':<30} {'速度':<8} {'准确性':<8} {'资源':<8} {'说明'}")
    print("-" * 80)

    for model, perf in MODEL_PERFORMANCE.items():
        print(f"{model:<30} {perf['speed']:<8} {perf['accuracy']:<8} "
              f"{perf['resource']:<8} {perf['description']}")

    print("\n💡 使用建议:")
    print("🚀 追求速度: --model fastest (7B-AWQ, 8倍提升)")
    print("⚖️  平衡选择: --model balanced (72B, 3倍提升)")
    print("🎯 最高准确性: --model current (235B, 当前速度)")

def switch_model(model_choice):
    """
    切换模型配置
    
    Args:
        model_choice: 模型选择
    
    Returns:
        dict: 新的配置
    """
    config = get_model_config(model_choice)
    
    print(f"✅ 已切换到模型: {config['model_name']}")
    
    if model_choice in ["fastest", "balanced", "accurate"]:
        perf = MODEL_PERFORMANCE.get(config['model_name'], {})
        multiplier = perf.get('estimated_speed_multiplier', 1)
        print(f"📈 预期速度提升: {multiplier}倍")
        print(f"📊 模型特点: {perf.get('description', '未知')}")
    
    return config

def get_recommended_config_for_task(task_type="classification"):
    """
    根据任务类型获取推荐配置
    
    Args:
        task_type: 任务类型
    
    Returns:
        dict: 推荐配置
    """
    if task_type == "classification":
        # 分类任务推荐平衡配置
        return get_model_config("balanced")
    elif task_type == "speed_priority":
        # 速度优先
        return get_model_config("fastest")
    elif task_type == "accuracy_priority":
        # 准确性优先
        return get_model_config("accurate")
    else:
        return get_model_config("balanced")

# 快速配置选项
QUICK_CONFIGS = {
    "ultra_fast": {
        "model": "fastest",
        "description": "极速模式: 7B模型 + 最大线程"
    },
    "recommended": {
        "model": "balanced", 
        "description": "推荐模式: 14B模型 + 平衡配置"
    },
    "high_quality": {
        "model": "accurate",
        "description": "高质量模式: 32B模型 + 保守配置"
    }
}

def main():
    """交互式模型配置"""
    print("🤖 费用分析模型配置工具")
    print("=" * 60)
    
    print_model_comparison()
    
    print("\n🔧 快速配置选项:")
    for key, config in QUICK_CONFIGS.items():
        print(f"{key}: {config['description']}")
    
    choice = input("\n请选择配置 (ultra_fast/recommended/high_quality): ").strip()
    
    if choice in QUICK_CONFIGS:
        model_choice = QUICK_CONFIGS[choice]["model"]
        config = switch_model(model_choice)
        
        print(f"\n✅ 配置完成!")
        print(f"📝 模型: {config['model_name']}")
        print(f"🌡️  温度: {config['temperature']}")
        print(f"⏱️  超时: {config['timeout']}秒")
        
        return config
    else:
        print("使用默认推荐配置")
        return get_model_config("balanced")

if __name__ == "__main__":
    main()
