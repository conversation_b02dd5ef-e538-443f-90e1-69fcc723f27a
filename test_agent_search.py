#!/usr/bin/env python3
"""
测试向量增强Agent的搜索功能
"""

from src.agents.vector_enhanced_agent import VectorEnhancedAgent
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO)

def test_agent_search():
    """测试向量增强Agent的搜索功能"""
    print("🤖 测试向量增强Agent的搜索功能")
    print("=" * 50)
    
    try:
        # 创建Agent实例
        agent = VectorEnhancedAgent()
        print(f"✅ Agent初始化成功，搜索阈值: {agent.vector_search_threshold}")
        
        # 测试搜索功能
        test_queries = [
            "当前电费情况如何",
            "电费情况", 
            "电费分析",
            "铁塔服务费",
            "指标对比",
            "数据异常"
        ]
        
        for test_query in test_queries:
            print(f"\n🔍 测试查询: '{test_query}'")
            try:
                # 直接调用搜索方法
                results = agent._search_vector_database(test_query)
                
                if results:
                    print(f"✅ 找到 {len(results)} 个匹配结果:")
                    for prompt, similarity in results:
                        print(f"  - {prompt.title}: {similarity:.3f}")
                else:
                    print("❌ 未找到匹配结果")
                    
            except Exception as e:
                print(f"❌ 搜索失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_agent_search()
