# AI费用分类系统

## 🎯 系统概述

AI费用分类系统是一个高效智能的费用原因分类工具，能够自动分析费用备注并智能分类。

### ⚡ 核心优势
- **超高速处理**：无备注记录毫秒级处理（891条/秒）
- **智能分类**：AI自动发现和分类费用原因
- **高准确性**：综合分析机制，置信度评估
- **安全可靠**：不修改原表，结果单独存储

## 🚀 快速使用

### 一键分类
```bash
# 基础分类（推荐）
python src/expense_analysis/classifier.py --month 202301 --clear-data

# 高性能分类（8线程）
python src/expense_analysis/classifier.py --month 202301 --model current --preset optimal_8core --clear-data

# 网络不稳定时使用（低并发）
python src/expense_analysis/classifier.py --month 202301 --model current --preset rate_limited --clear-data

# 分类指定地市数据
python src/expense_analysis/classifier.py --month 202301 --city 440100 --clear-data
```

### 📋 完整参数说明

| 参数 | 必填 | 默认值 | 说明 | 示例 |
|------|------|--------|------|------|
| `--month` | ✅ | - | 月份(YYYYMM格式) | `--month 202301` |
| `--clear-data` | ❌ | false | 清理已有数据重新分类 | `--clear-data` |
| `--model` | ❌ | balanced | AI模型选择 | `--model current` |
| `--preset` | ❌ | balanced | 性能配置预设 | `--preset optimal_8core` |
| `--city` | ❌ | - | 指定地市ID | `--city 440100` |
| `--table` | ❌ | clean_ele_payment | 数据表名 | `--table clean_ele_payment` |
| `--llm` | ❌ | local | LLM提供商 | `--llm local` |

### 🎯 模型选择 (--model)

| 选项 | 模型规模 | 速度 | 准确性 | 适用场景 |
|------|----------|------|--------|----------|
| `fastest` | 7B | ⚡⚡⚡ | ⭐⭐ | 快速测试 |
| `balanced` | 14B | ⚡⚡ | ⭐⭐⭐ | 日常使用 |
| `accurate` | 32B | ⚡ | ⭐⭐⭐⭐ | 高精度需求 |
| `current` | 235B | ⚡ | ⭐⭐⭐⭐⭐ | 最佳效果（推荐） |

### ⚙️ 性能预设 (--preset)

| 预设 | 线程数 | 批次大小 | 适用场景 |
|------|--------|----------|----------|
| `conservative` | 1 | 10 | 单线程，最稳定 |
| `balanced` | 2 | 20 | 平衡性能和稳定性 |
| `optimal_8core` | 4 | 30-80 | 8核CPU优化（推荐） |
| `rate_limited` | 2 | 20 | 网络不稳定，避免502错误 |
| `rate_limited_fast` | 3 | 25 | 轻微网络问题 |
| `max_performance` | 10 | 15-200 | 最高性能，网络稳定时使用 |

### 查看结果
```bash
# 查看分类统计
python src/expense_analysis/expense_classifier.py --table clean_ele_payment --mode summary --month 202301

# 查看审核状态分布
SELECT
    CASE
        WHEN auditing_state = 0 THEN '审核通过'
        WHEN auditing_state = 8 THEN '审核失败'
        WHEN auditing_state = 9 THEN '审核中'
        WHEN auditing_state = -1 THEN '未提交'
        ELSE CONCAT('未知状态(', auditing_state, ')')
    END AS 审核状态,
    COUNT(1) AS 记录数
FROM ele_payment_ai_sort
WHERE rpt_month = '202301'
GROUP BY auditing_state
ORDER BY auditing_state;
```

## 📊 处理效果

### 性能表现
- **处理速度**：2,000+ 条/小时
- **无备注处理**：891 条/秒（毫秒级）
- **AI分类速度**：30-50 条/分钟
- **准确率**：95%+ （基于置信度评估）

### 典型分类结果
```
202301月份分类结果:
==================================================
无备注: 75,000 条 (97.8%)
清单序号定位: 800 条 (1.0%)
业务流程问题: 500 条 (0.7%)
优惠或减免政策: 200 条 (0.3%)
自控成本支付: 150 条 (0.2%)
```

## 🔧 环境配置

### 数据库配置
确保数据库连接配置正确：
```python
# src/config/database_config.py
DATABASE_CONFIG = {
    'host': 'your_host',
    'port': 3306,
    'user': 'your_user',
    'password': 'your_password',
    'database': 'your_database'
}
```

### AI服务配置
确保本地LLM服务正常运行：
```bash
# 检查AI服务状态
curl http://127.0.0.1:33210/health
```

## 📋 分类结果表

### DDL语句

执行以下DDL语句创建费用分类结果表：

```sql
-- 创建费用分类结果表
CREATE TABLE `ele_payment_ai_sort` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `billaccountpaymentdetail_id` varchar(32) NOT NULL COMMENT '账单支付明细ID',
  `payment_code` varchar(50) DEFAULT NULL COMMENT '支付代码',
  `preg_id` varchar(32) DEFAULT NULL COMMENT '省份ID',
  `preg_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
  `reg_id` varchar(32) DEFAULT NULL COMMENT '地市ID',
  `reg_name` varchar(50) DEFAULT NULL COMMENT '地市名称',
  `rpt_month` varchar(6) DEFAULT NULL COMMENT '报告月份(YYYYMM)',
  `original_remark` text COMMENT '原始备注内容',
  `ai_category` varchar(100) DEFAULT NULL COMMENT 'AI分类结果',
  `ai_confidence` decimal(5,4) DEFAULT NULL COMMENT 'AI分类置信度(0-1)',
  `ai_classified_time` datetime DEFAULT NULL COMMENT 'AI分类时间',
  `billamount_startdate` date DEFAULT NULL COMMENT '账单金额开始日期',
  `billamount_enddate` date DEFAULT NULL COMMENT '账单金额结束日期',
  `auditing_state` int(11) DEFAULT NULL COMMENT '审核状态: 0=审核通过, 8=审核失败, 9=审核中, -1=未提交',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_billaccountpaymentdetail_id` (`billaccountpaymentdetail_id`),
  KEY `idx_payment_code` (`payment_code`),
  KEY `idx_preg_id` (`preg_id`),
  KEY `idx_rpt_month` (`rpt_month`),
  KEY `idx_ai_category` (`ai_category`),
  KEY `idx_auditing_state` (`auditing_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='费用分类AI分析结果表';
```

### 🔄 数据库升级

如果您已有旧版本的表，请执行以下SQL语句添加新字段：

```sql
-- 添加审核状态字段
ALTER TABLE `ele_payment_ai_sort`
ADD COLUMN `auditing_state` int(11) DEFAULT NULL COMMENT '审核状态: 0=审核通过, 8=审核失败, 9=审核中, -1=未提交'
AFTER `billamount_enddate`;

-- 添加索引以提升查询性能
ALTER TABLE `ele_payment_ai_sort`
ADD INDEX `idx_auditing_state` (`auditing_state`);
```

### 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | bigint(20) | 主键ID，自增 |
| `billaccountpaymentdetail_id` | varchar(32) | 账单支付明细ID，唯一标识 |
| `payment_code` | varchar(50) | 支付代码 |
| `preg_id` | varchar(32) | 省份ID |
| `preg_name` | varchar(50) | 省份名称 |
| `reg_id` | varchar(32) | 地市ID |
| `reg_name` | varchar(50) | 地市名称 |
| `rpt_month` | varchar(6) | 报告月份(YYYYMM格式) |
| `original_remark` | text | 原始备注内容 |
| `ai_category` | varchar(100) | AI分类结果 |
| `ai_confidence` | decimal(5,4) | AI分类置信度(0-1) |
| `ai_classified_time` | datetime | AI分类时间 |
| `billamount_startdate` | date | **账单金额开始日期** ✨ |
| `billamount_enddate` | date | **账单金额结束日期** ✨ |
| `auditing_state` | int(11) | **审核状态** 🔍 |
| `create_time` | timestamp | 创建时间 |
| `update_time` | timestamp | 更新时间 |

### 🔍 审核状态说明
| 状态值 | 含义 |
|--------|------|
| `0` | 审核通过 |
| `8` | 审核失败 |
| `9` | 审核中 |
| `-1` | 未提交 |

> ✨ **新增字段**:
> - `billamount_startdate` 和 `billamount_enddate` 用于存储来自 `clean_ele_payment` 表的账单日期信息
> - `auditing_state` 用于存储来自 `clean_ele_payment` 表的审核状态信息

## 🎯 使用场景

### 场景1：首次分类（推荐）
```bash
# 高性能分类，清理重新处理
python src/expense_analysis/classifier.py --month 202301 --model current --preset optimal_8core --clear-data
```

### 场景2：网络不稳定环境
```bash
# 低并发，避免502/429错误
python src/expense_analysis/classifier.py --month 202301 --model current --preset rate_limited --clear-data
```

### 场景3：增量处理
```bash
# 只处理新增或失败的记录（不清理已有数据）
python src/expense_analysis/classifier.py --month 202301 --model current --preset balanced
```

### 场景4：指定地市
```bash
# 只分类特定地市数据
python src/expense_analysis/classifier.py --month 202301 --city 440100 --model current --preset optimal_8core --clear-data
```

### 场景5：快速测试
```bash
# 使用最快模型进行测试
python src/expense_analysis/classifier.py --month 202301 --model fastest --preset conservative --clear-data
```

## 📈 系统优化

### 智能预筛选
- 自动识别无备注记录，毫秒级处理
- 只对有备注记录调用AI，大幅提升效率

### AI加速优化
- 使用no_think模式，提升2倍处理速度
- 自适应批量大小，根据性能动态调整

### 错误恢复
- 自动重试机制，处理网络超时
- 失败记录单独标记，可后续重试

## ⚠️ 注意事项

### 数据安全
- ✅ 分类结果保存到新表，不修改原表
- ✅ 支持数据清理，避免重复分类
- ✅ 使用 `ON DUPLICATE KEY UPDATE`，安全更新

### 性能建议
- 建议按月份分批处理，避免一次性处理过多数据
- 确保AI服务网络连接稳定
- 定期检查分类结果准确性

### 故障排除

#### 常见错误及解决方案

| 错误类型 | 错误信息 | 解决方案 |
|----------|----------|----------|
| **502 Bad Gateway** | `API request failed: 502` | 使用 `--preset rate_limited` 降低并发 |
| **429 Too Many Requests** | `Too Many Requests` | 系统会自动重试，或使用 `--preset rate_limited` |
| **网络超时** | `Timeout` | 增加 `--preset conservative` 或检查网络 |
| **参数错误** | `invalid choice` | 检查参数拼写，参考上方参数表 |

#### 性能优化建议
- **首次使用**：推荐 `--preset optimal_8core --clear-data`
- **网络不稳定**：使用 `--preset rate_limited`
- **大量数据**：分月份处理，避免一次性处理过多数据
- **测试环境**：使用 `--model fastest --preset conservative`

## 🎉 系统优势

1. **高效智能**：集成多种优化技术，处理速度快
2. **准确可靠**：AI自主分析，置信度评估
3. **简单易用**：一键分类，参数简单
4. **安全稳定**：数据安全，错误恢复
5. **扩展性强**：支持新分类自动发现

**AI费用分类系统让费用分析变得简单高效！**
