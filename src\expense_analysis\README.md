# AI费用分类系统

## 🎯 系统概述

AI费用分类系统是一个高效智能的费用原因分类工具，能够自动分析费用备注并智能分类。

### ⚡ 核心优势
- **超高速处理**：无备注记录毫秒级处理（891条/秒）
- **智能分类**：AI自动发现和分类费用原因
- **高准确性**：综合分析机制，置信度评估
- **安全可靠**：不修改原表，结果单独存储

## 🚀 快速使用

### 一键分类
```bash
# 分类指定月份数据
python src/expense_analysis/classifier.py --month 202301

# 清理并重新分类
python src/expense_analysis/classifier.py --month 202301 --clear-data

# 分类指定地市数据
python src/expense_analysis/classifier.py --month 202301 --city 440100
```

### 查看结果
```bash
# 查看分类统计
python src/expense_analysis/expense_classifier.py --table clean_ele_payment --mode summary --month 202301
```

## 📊 处理效果

### 性能表现
- **处理速度**：2,000+ 条/小时
- **无备注处理**：891 条/秒（毫秒级）
- **AI分类速度**：30-50 条/分钟
- **准确率**：95%+ （基于置信度评估）

### 典型分类结果
```
202301月份分类结果:
==================================================
无备注: 75,000 条 (97.8%)
清单序号定位: 800 条 (1.0%)
业务流程问题: 500 条 (0.7%)
优惠或减免政策: 200 条 (0.3%)
自控成本支付: 150 条 (0.2%)
```

## 🔧 环境配置

### 数据库配置
确保数据库连接配置正确：
```python
# src/config/database_config.py
DATABASE_CONFIG = {
    'host': 'your_host',
    'port': 3306,
    'user': 'your_user',
    'password': 'your_password',
    'database': 'your_database'
}
```

### AI服务配置
确保本地LLM服务正常运行：
```bash
# 检查AI服务状态
curl http://127.0.0.1:33210/health
```

## 📋 分类结果表

系统会自动创建 `ele_payment_ai_sort` 表存储分类结果：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `billaccountpaymentdetail_id` | VARCHAR | 原始记录ID |
| `payment_code` | VARCHAR | 支付代码 |
| `preg_id` | VARCHAR | 地市ID |
| `rpt_month` | VARCHAR | 报告月份 |
| `original_remark` | TEXT | 原始备注内容 |
| `ai_category` | VARCHAR | AI分类结果 |
| `ai_confidence` | DECIMAL | 置信度（0-1） |
| `ai_classified_time` | DATETIME | 分类时间 |

## 🎯 使用场景

### 场景1：月度分类
```bash
# 分类当月数据
python src/expense_analysis/classifier.py --month 202301
```

### 场景2：重新分类
```bash
# 清理并重新分类（数据有更新时）
python src/expense_analysis/classifier.py --month 202301 --clear-data
```

### 场景3：指定地市
```bash
# 只分类特定地市数据
python src/expense_analysis/classifier.py --month 202301 --city 440100
```

## 📈 系统优化

### 智能预筛选
- 自动识别无备注记录，毫秒级处理
- 只对有备注记录调用AI，大幅提升效率

### AI加速优化
- 使用no_think模式，提升2倍处理速度
- 自适应批量大小，根据性能动态调整

### 错误恢复
- 自动重试机制，处理网络超时
- 失败记录单独标记，可后续重试

## ⚠️ 注意事项

### 数据安全
- ✅ 分类结果保存到新表，不修改原表
- ✅ 支持数据清理，避免重复分类
- ✅ 使用 `ON DUPLICATE KEY UPDATE`，安全更新

### 性能建议
- 建议按月份分批处理，避免一次性处理过多数据
- 确保AI服务网络连接稳定
- 定期检查分类结果准确性

### 故障排除
- **AI超时**：系统会自动重试，使用默认分类
- **网络问题**：检查AI服务连接状态
- **分类异常**：查看置信度，低置信度记录可重新分类

## 🎉 系统优势

1. **高效智能**：集成多种优化技术，处理速度快
2. **准确可靠**：AI自主分析，置信度评估
3. **简单易用**：一键分类，参数简单
4. **安全稳定**：数据安全，错误恢复
5. **扩展性强**：支持新分类自动发现

**AI费用分类系统让费用分析变得简单高效！**
