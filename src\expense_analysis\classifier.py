#!/usr/bin/env python3
"""
费用分类器 - 主程序
集成所有优化：预筛选、no_think、自适应批量等
"""
import sys
import os
import argparse
import time
from typing import List, Dict, Tuple

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.expense_analysis.services.data_service import DataService
from src.expense_analysis.agents import ExpenseClassificationAgent


class ExpenseClassifier:
    """费用分类器 - 集成所有优化"""
    
    def __init__(self, llm_provider: str = "local"):
        self.agent = ExpenseClassificationAgent(llm_provider)
        self.data_service = DataService()
    
    def classify_all(self, table_name: str, filters: dict = None, clear_data: bool = False):
        """智能分类处理 - 集成所有优化"""
        print("🚀 启动智能费用分类")

        # 可选：清理已有数据
        if clear_data:
            self._clear_existing_data(filters)
        
        # 步骤1：预筛选无备注和有备注记录
        no_remark_records, with_remark_records = self._presort_records(table_name, filters)
        
        # 步骤2：批量处理无备注记录（毫秒级）
        self._batch_process_no_remark(no_remark_records)
        
        # 步骤3：AI处理有备注记录
        if with_remark_records:
            self._ai_process_with_remark(with_remark_records)
        
        print("🎉 智能分类完成！")

    def _clear_existing_data(self, filters: dict = None):
        """清理已有分类数据"""
        print("🧹 清理已有分类数据...")

        with self.data_service.get_connection() as conn:
            cursor = conn.cursor()

            if filters and filters.get('rpt_month'):
                # 只清理指定月份的数据
                sql = "DELETE FROM ele_payment_ai_sort WHERE rpt_month = %s"
                cursor.execute(sql, (filters['rpt_month'],))
                deleted_count = cursor.rowcount
                print(f"✅ 已清理 {filters['rpt_month']} 月份的 {deleted_count} 条记录")
            else:
                # 清理所有数据（谨慎使用）
                confirm = input("⚠️ 确认清理所有分类数据？(输入 'YES' 确认): ")
                if confirm == 'YES':
                    sql = "DELETE FROM ele_payment_ai_sort"
                    cursor.execute(sql)
                    deleted_count = cursor.rowcount
                    print(f"✅ 已清理所有 {deleted_count} 条记录")
                else:
                    print("❌ 取消清理操作")
                    return

            conn.commit()
    
    def _presort_records(self, table_name: str, filters: dict = None) -> Tuple[List, List]:
        """预筛选记录：分离无备注和有备注"""
        print("📊 预筛选记录...")
        
        with self.data_service.get_connection() as conn:
            cursor = conn.cursor()
            
            # 基础SQL
            base_sql = f"""
            SELECT billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
                   reg_id, reg_name, billamount_date, paymentdetail_note,
                   mandatory_note, search_keywords, auditing_state,
                   last_review_result, last_review_comment, billamount_startdate, billamount_enddate
            FROM {table_name}
            WHERE billaccountpaymentdetail_id NOT IN (
                SELECT billaccountpaymentdetail_id FROM ele_payment_ai_sort
            )
            """
            
            params = []
            
            # 添加过滤条件
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            year = value[:4]
                            month = value[4:6]
                            year_month = f"{year}-{month}"
                            base_sql += " AND %s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')"
                            params.append(year_month)
                        elif key == 'preg_id':
                            base_sql += " AND preg_id = %s"
                            params.append(value)
            
            # 不限制数量，处理所有记录
            
            cursor.execute(base_sql, params)
            all_records = cursor.fetchall()

            # 同时查询总记录数用于对比
            total_sql = f"""
            SELECT count(1)
            FROM {table_name}
            WHERE 1=1
            """
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            year = value[:4]
                            month = value[4:6]
                            year_month = f"{year}-{month}"
                            total_sql += " AND %s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')"

            cursor.execute(total_sql, params)
            total_records = cursor.fetchone()[0]

            print(f"📋 月份总记录: {total_records} 条")
            print(f"📋 待处理记录: {len(all_records)} 条")
            print(f"📋 已处理记录: {total_records - len(all_records)} 条")
            
            # 分离无备注和有备注记录
            no_remark_records = []
            with_remark_records = []
            
            for row in all_records:
                paymentdetail_note = row[7]  # paymentdetail_note字段
                mandatory_note = row[8]      # mandatory_note字段
                
                # 判断是否有有效备注
                has_remark = False
                
                # 检查paymentdetail_note
                if paymentdetail_note and paymentdetail_note.strip():
                    note = paymentdetail_note.strip()
                    if (note != '[]' and note != 'null' and note != '无' and 
                        note != '退回' and note != '0' and note != '1' and 
                        len(note) > 3):
                        has_remark = True
                
                # 检查mandatory_note
                if not has_remark and mandatory_note and mandatory_note.strip():
                    note = mandatory_note.strip()
                    if (note != 'null' and note != '无' and note != '退回' and 
                        len(note) > 3):
                        has_remark = True
                
                if has_remark:
                    with_remark_records.append(row)
                else:
                    no_remark_records.append(row)
            
            print(f"📊 预筛选结果:")
            print(f"   📝 有备注记录: {len(with_remark_records)} 条 ({len(with_remark_records)/len(all_records)*100:.1f}%)")
            print(f"   📄 无备注记录: {len(no_remark_records)} 条 ({len(no_remark_records)/len(all_records)*100:.1f}%)")
            
            return no_remark_records, with_remark_records
    
    def _batch_process_no_remark(self, no_remark_records: List):
        """批量处理无备注记录（毫秒级）"""
        if not no_remark_records:
            print("📄 没有无备注记录需要处理")
            return
        
        print(f"⚡ 批量处理 {len(no_remark_records)} 条无备注记录...")
        
        # 确保分类结果表存在
        self.data_service._ensure_classification_table()
        
        start_time = time.time()
        
        with self.data_service.get_connection() as conn:
            cursor = conn.cursor()
            
            # 准备批量插入数据
            insert_data = []
            current_time = time.strftime('%Y-%m-%d %H:%M:%S')
            
            for row in no_remark_records:
                # 计算rpt_month
                billamount_startdate = row[13]  # billamount_startdate
                if billamount_startdate:
                    rpt_month = billamount_startdate.strftime('%Y%m')
                else:
                    rpt_month = None
                
                insert_data.append((
                    row[0],  # billaccountpaymentdetail_id
                    row[1],  # payment_code
                    row[2],  # preg_id
                    row[3],  # preg_name
                    row[4],  # reg_id
                    row[5],  # reg_name
                    rpt_month,
                    '',      # original_remark (空)
                    '无备注', # ai_category
                    1.0,     # ai_confidence (100%确定)
                    current_time
                ))
            
            # 批量插入
            sql = """
            INSERT INTO ele_payment_ai_sort 
            (billaccountpaymentdetail_id, payment_code, preg_id, preg_name, 
             reg_id, reg_name, rpt_month, original_remark, ai_category, 
             ai_confidence, ai_classified_time)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            ai_category = VALUES(ai_category),
            ai_confidence = VALUES(ai_confidence),
            ai_classified_time = VALUES(ai_classified_time)
            """
            
            cursor.executemany(sql, insert_data)
            conn.commit()
        
        duration = time.time() - start_time
        speed = len(no_remark_records) / duration
        
        print(f"✅ 无备注记录处理完成:")
        print(f"   📊 处理数量: {len(no_remark_records)} 条")
        print(f"   ⏱️  用时: {duration:.2f}秒")
        print(f"   🚀 速度: {speed:.0f} 条/秒")
    
    def _ai_process_with_remark(self, with_remark_records: List):
        """AI处理有备注记录"""
        print(f"🤖 AI处理 {len(with_remark_records)} 条有备注记录...")
        
        # 转换为ExpenseRecord对象
        records = []
        for row in with_remark_records:
            # 计算rpt_month
            billamount_startdate = row[13]
            if billamount_startdate:
                rpt_month = billamount_startdate.strftime('%Y%m')
            else:
                rpt_month = None
            
            record = self.data_service._dict_to_expense_record(
                dict(zip([
                    'billaccountpaymentdetail_id', 'payment_code', 'preg_id', 'preg_name',
                    'reg_id', 'reg_name', 'billamount_date', 'paymentdetail_note',
                    'mandatory_note', 'search_keywords', 'auditing_state',
                    'last_review_result', 'last_review_comment', 'billamount_startdate', 'billamount_enddate'
                ], row)), 
                rpt_month
            )
            records.append(record)
        
        # 提取综合备注
        comprehensive_remarks = self.data_service.get_comprehensive_remarks_for_classification(records)
        if not comprehensive_remarks:
            print("❌ 没有提取到有效的备注内容")
            return
        
        print(f"📝 提取到 {len(comprehensive_remarks)} 条有效备注")
        
        # 加载分类类别
        categories = self._load_categories()
        
        # 使用优化的AI分类（自适应批量，高效处理）
        start_time = time.time()
        print(f"🤖 开始AI分类，预计用时: {len(comprehensive_remarks)*0.5/60:.1f}分钟")

        # 集成所有优化：no_think + 大批量 + 实时保存
        classification_results = self._turbo_classify_with_realtime_save(
            comprehensive_remarks,
            categories,
            records
        )
        
        ai_duration = time.time() - start_time
        ai_speed = len(comprehensive_remarks) / ai_duration if ai_duration > 0 else 0
        
        print(f"✅ AI处理完成:")
        print(f"   📊 处理数量: {len(comprehensive_remarks)} 条")
        print(f"   ⏱️  用时: {ai_duration/60:.1f}分钟")
        print(f"   🚀 速度: {ai_speed:.1f} 条/秒")
        
        # 保存AI分类结果
        self._save_ai_results(records, classification_results)
    
    def _load_categories(self):
        """加载分类类别"""
        try:
            import json
            categories_file = os.path.join(os.path.dirname(__file__), "discovered_categories.json")
            with open(categories_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('categories', [])
        except:
            return [
                "无备注",
                "清单序号定位",
                "数据异常处理",
                "业务流程问题",
                "其他原因"
            ]
    
    def _save_ai_results(self, records: List, classification_results: Dict):
        """保存AI分类结果"""
        print("💾 保存AI分类结果...")
        
        saved_count = 0
        
        with self.data_service.get_connection() as conn:
            cursor = conn.cursor()
            
            for record in records:
                record_id = record.billaccountpaymentdetail_id
                
                if record_id in classification_results:
                    result = classification_results[record_id]
                    
                    # 处理结果格式
                    if hasattr(result, 'category'):
                        category = result.category
                        confidence = result.confidence
                    else:
                        category = result.get('category', '其他费用')
                        confidence = result.get('confidence', 0.1)
                    
                    # 收集原始备注
                    original_remarks = []
                    if record.paymentdetail_note and record.paymentdetail_note.strip():
                        original_remarks.append(record.paymentdetail_note.strip())
                    if record.mandatory_note and record.mandatory_note.strip():
                        original_remarks.append(f"[必填] {record.mandatory_note.strip()}")
                    
                    # 插入或更新记录
                    sql = """
                    INSERT INTO ele_payment_ai_sort 
                    (billaccountpaymentdetail_id, payment_code, preg_id, preg_name, 
                     reg_id, reg_name, rpt_month, original_remark, ai_category, 
                     ai_confidence, ai_classified_time)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    ON DUPLICATE KEY UPDATE
                    ai_category = VALUES(ai_category),
                    ai_confidence = VALUES(ai_confidence),
                    ai_classified_time = VALUES(ai_classified_time)
                    """
                    
                    cursor.execute(sql, (
                        record.billaccountpaymentdetail_id,
                        record.payment_code,
                        record.preg_id,
                        record.preg_name,
                        record.reg_id,
                        record.reg_name,
                        record.rpt_month,
                        '; '.join(original_remarks),
                        category,
                        confidence
                    ))
                    
                    saved_count += 1
            
            conn.commit()
        
        print(f"✅ 已保存 {saved_count} 条AI分类结果")

    def _turbo_classify_with_realtime_save(self, comprehensive_remarks, categories, records):
        """极速分类 + 实时保存 - 集成所有优化"""
        import time

        print(f"🚀 启动极速分类模式")
        print(f"⚡ 优化特性: no_think + 大批量 + 实时保存 + 自适应")

        # 创建记录ID到记录的映射
        record_map = {record.billaccountpaymentdetail_id: record for record in records}

        # 极速配置
        batch_size = 40  # 初始批量
        max_batch_size = 80  # 最大批量
        total_saved = 0

        print(f"🔥 极速配置:")
        print(f"   初始批量: {batch_size}")
        print(f"   最大批量: {max_batch_size}")
        print(f"   实时保存: 每批次完成后立即保存")
        print(f"   no_think: 已启用")

        i = 0
        batch_num = 1
        start_time = time.time()

        while i < len(comprehensive_remarks):
            batch = comprehensive_remarks[i:i + batch_size]

            print(f"🚀 批次 {batch_num}, 大小: {len(batch)}")

            batch_start = time.time()

            try:
                # AI分类
                batch_results = self.agent._classify_comprehensive_batch(batch, categories)

                batch_duration = time.time() - batch_start
                batch_speed = len(batch) / batch_duration

                print(f"✅ 批次完成 (用时: {batch_duration:.1f}秒, 速度: {batch_speed:.1f}条/秒)")

                # 立即保存这批次的结果
                batch_saved = self._save_batch_results(batch, batch_results, record_map)
                total_saved += batch_saved
                print(f"💾 已保存 {batch_saved} 条到数据库 (累计: {total_saved})")

                # 自适应批量调整
                if batch_duration < 20:  # 很快完成
                    batch_size = min(batch_size + 10, max_batch_size)
                    print(f"⚡ 性能优秀，批量增加到: {batch_size}")
                elif batch_duration < 35:  # 正常速度
                    batch_size = min(batch_size + 5, max_batch_size)
                    print(f"📈 稳步增加批量到: {batch_size}")
                elif batch_duration > 50:  # 接近超时
                    batch_size = max(batch_size - 10, 15)
                    print(f"🔽 避免超时，减少批量到: {batch_size}")
                elif batch_duration >= 60:  # 超时了
                    batch_size = max(batch_size - 15, 10)
                    print(f"⚠️ 超时，大幅减少批量到: {batch_size}")

                # 计算进度
                progress = (i + len(batch)) / len(comprehensive_remarks) * 100
                elapsed = time.time() - start_time
                if progress > 0:
                    estimated_total = elapsed / (progress / 100)
                    remaining = estimated_total - elapsed
                    print(f"📈 进度: {progress:.1f}% (预计剩余: {remaining/60:.1f}分钟)")

            except Exception as e:
                print(f"❌ 批次失败: {str(e)[:100]}...")
                # 失败时也保存默认分类
                default_results = {}
                for remark_info in batch:
                    default_results[remark_info['record_id']] = {
                        'category': "其他费用",
                        'confidence': 0.1,
                        'reason': f"处理失败"
                    }
                batch_saved = self._save_batch_results(batch, default_results, record_map)
                total_saved += batch_saved
                print(f"💾 失败批次已保存默认分类 {batch_saved} 条")

            i += len(batch)
            batch_num += 1

        total_duration = time.time() - start_time
        speed = len(comprehensive_remarks) / total_duration

        print(f"🎉 极速分类完成:")
        print(f"   📊 处理数量: {len(comprehensive_remarks)} 条")
        print(f"   💾 已保存数量: {total_saved} 条")
        print(f"   ⏱️  用时: {total_duration/60:.1f}分钟")
        print(f"   🚀 速度: {speed:.1f} 条/秒")

        return {}  # 返回空字典，因为已经实时保存了

    def _save_batch_results(self, batch, batch_results, record_map):
        """保存单个批次的结果到数据库"""
        saved_count = 0

        with self.data_service.get_connection() as conn:
            cursor = conn.cursor()

            for remark_info in batch:
                record_id = remark_info['record_id']

                if record_id in batch_results and record_id in record_map:
                    result = batch_results[record_id]
                    record = record_map[record_id]

                    if hasattr(result, 'category'):
                        category = result.category
                        confidence = result.confidence
                    else:
                        category = result.get('category', '其他费用')
                        confidence = result.get('confidence', 0.1)

                    original_remarks = []
                    if record.paymentdetail_note and record.paymentdetail_note.strip():
                        original_remarks.append(record.paymentdetail_note.strip())
                    if record.mandatory_note and record.mandatory_note.strip():
                        original_remarks.append(f"[必填] {record.mandatory_note.strip()}")

                    sql = """
                    INSERT INTO ele_payment_ai_sort
                    (billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
                     reg_id, reg_name, rpt_month, original_remark, ai_category,
                     ai_confidence, ai_classified_time)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    ON DUPLICATE KEY UPDATE
                    ai_category = VALUES(ai_category),
                    ai_confidence = VALUES(ai_confidence),
                    ai_classified_time = VALUES(ai_classified_time)
                    """

                    cursor.execute(sql, (
                        record.billaccountpaymentdetail_id,
                        record.payment_code,
                        record.preg_id,
                        record.preg_name,
                        record.reg_id,
                        record.reg_name,
                        record.rpt_month,
                        '; '.join(original_remarks),
                        category,
                        confidence
                    ))

                    saved_count += 1

            conn.commit()

        return saved_count


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='AI费用分类器 - 智能高效的费用原因分类',
        epilog="""
使用示例:
  # 分类202301月份数据
  python ultra_fast_classifier.py --month 202301

  # 清理并重新分类
  python ultra_fast_classifier.py --month 202301 --clear-data

  # 分类指定地市数据
  python ultra_fast_classifier.py --month 202301 --city 440100
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument('--table', default='clean_ele_payment', help='数据表名')
    parser.add_argument('--month', required=True, help='月份 (YYYYMM格式，如202301)')
    parser.add_argument('--city', help='地市ID (可选)')
    parser.add_argument('--clear-data', action='store_true', help='清理已有分类数据')
    parser.add_argument('--llm', default='local', help='LLM提供商')

    args = parser.parse_args()

    # 验证月份格式
    if len(args.month) != 6 or not args.month.isdigit():
        print("❌ 错误：月份格式应为YYYYMM，如202301")
        return

    # 构建过滤条件
    filters = {'rpt_month': args.month}
    if args.city:
        filters['preg_id'] = args.city

    # 创建分类器并执行
    classifier = ExpenseClassifier(args.llm)
    classifier.classify_all(args.table, filters, args.clear_data)


if __name__ == '__main__':
    main()
