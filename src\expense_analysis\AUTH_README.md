# 费用分析API鉴权说明

## 默认Token
```
expense_api_2025_secure_token
```

## 使用方式

### 1. 在请求头中添加Bearer Token
```bash
curl -H "Authorization: Bearer expense_api_2025_secure_token" \
     "http://localhost:8002/api/statistics"
```

### 2. 浏览器访问（需要插件或工具）
在浏览器中直接访问需要使用支持Bearer Token的工具，如：
- Postman
- Insomnia  
- 浏览器插件（如ModHeader）

### 3. 使用FastAPI文档页面
访问 http://localhost:8002/docs
1. 点击右上角的 "Authorize" 按钮
2. 输入token: `expense_api_2025_secure_token`
3. 点击 "Authorize"
4. 现在可以在文档页面测试所有接口

## 接口列表

### 需要鉴权的接口：
- `GET /export/statistics` - 导出统计Excel
- `GET /export/details` - 导出明细Excel  
- `GET /api/statistics` - 获取JSON统计数据
- `GET /auth/info` - 获取token信息

### 无需鉴权的接口：
- `GET /health` - 健康检查

## 配置选项

### 1. 添加自定义Token（环境变量）
```bash
export EXPENSE_API_TOKEN=your_custom_token
```

### 2. 开发时关闭鉴权
```bash
export EXPENSE_API_AUTH_DISABLED=true
```

### 3. 修改默认Token
编辑 `src/expense_analysis/config.py` 文件中的 `VALID_TOKENS`

## 测试示例

```bash
# 健康检查（无需token）
curl http://localhost:8002/health

# 测试鉴权
curl -H "Authorization: Bearer expense_api_2025_secure_token" \
     http://localhost:8002/auth/info

# 获取统计数据
curl -H "Authorization: Bearer expense_api_2025_secure_token" \
     "http://localhost:8002/api/statistics?rpt_month=202501"

# 导出Excel（会下载文件）
curl -H "Authorization: Bearer expense_api_2025_secure_token" \
     "http://localhost:8002/export/statistics" \
     -o statistics.xlsx
```
