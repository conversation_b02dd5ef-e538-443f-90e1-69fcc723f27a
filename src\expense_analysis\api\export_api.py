"""
费用分类导出API
"""
import os
import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException, Query, Depends
from fastapi.responses import FileResponse
import tempfile

from ..services.data_service import DataService
from ..auth import verify_token
from ..auth_config import AuthConfig

# 创建FastAPI应用实例
app = FastAPI(title="费用分析导出API", description="提供费用分类统计和明细数据的导出功能")

# 初始化数据服务
data_service = DataService()

@app.get("/export/statistics")
async def export_statistics(
    rpt_month: Optional[str] = Query(None, description="报账月份(YYYYMM)"),
    preg_id: Optional[str] = Query(None, description="地市ID"),
    token: str = Depends(verify_token)
):
    """导出分类统计结果"""
    try:
        filters = {}
        if rpt_month:
            filters['rpt_month'] = rpt_month
        if preg_id:
            filters['preg_id'] = preg_id
        
        # 获取统计数据
        stats = data_service.get_classification_statistics(filters)
        
        if not stats:
            raise HTTPException(status_code=404, detail="未找到分类数据")
        
        # 转换为DataFrame
        df = pd.DataFrame([
            {'分类类别': category, '数量': count, '占比': f"{count/sum(stats.values())*100:.1f}%"}
            for category, count in stats.items()
        ])
        
        # 生成临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xlsx', delete=False, encoding='utf-8') as f:
            temp_path = f.name
        
        # 写入Excel
        with pd.ExcelWriter(temp_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='分类统计', index=False)
        
        # 生成文件名
        filename = f"费用分类统计_{rpt_month or '全部'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        return FileResponse(
            path=temp_path,
            filename=filename,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.get("/export/details")
async def export_details(
    category: str = Query(..., description="分类类别"),
    rpt_month: Optional[str] = Query(None, description="报账月份(YYYYMM)"),
    preg_id: Optional[str] = Query(None, description="地市ID"),
    token: str = Depends(verify_token)
):
    """导出指定分类的明细数据"""
    try:
        filters = {}
        if rpt_month:
            filters['rpt_month'] = rpt_month
        if preg_id:
            filters['preg_id'] = preg_id
        
        # 获取明细数据
        details = data_service.get_classification_details(category, filters)
        
        if not details:
            raise HTTPException(status_code=404, detail=f"未找到分类 '{category}' 的明细数据")
        
        # 转换为DataFrame
        df = pd.DataFrame(details)
        
        # 重命名列
        column_mapping = {
            'billaccountpaymentdetail_id': '缴费明细ID',
            'payment_code': '缴费单编码',
            'preg_name': '地市名称',
            'reg_name': '区县名称',
            'rpt_month': '报账月份',
            'original_remark': '原始备注',
            'ai_category': 'AI分类',
            'ai_confidence': '置信度'
        }
        
        df = df.rename(columns=column_mapping)
        
        # 格式化置信度
        if '置信度' in df.columns:
            df['置信度'] = df['置信度'].apply(lambda x: f"{x:.2%}" if pd.notnull(x) else "")
        
        # 生成临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xlsx', delete=False, encoding='utf-8') as f:
            temp_path = f.name
        
        # 写入Excel
        with pd.ExcelWriter(temp_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=f'{category}明细', index=False)
        
        # 生成文件名
        filename = f"费用明细_{category}_{rpt_month or '全部'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        return FileResponse(
            path=temp_path,
            filename=filename,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.get("/api/statistics")
async def get_statistics(
    rpt_month: Optional[str] = Query(None, description="报账月份(YYYYMM)"),
    preg_id: Optional[str] = Query(None, description="地市ID"),
    token: str = Depends(verify_token)
):
    """获取分类统计数据（JSON格式）"""
    try:
        filters = {}
        if rpt_month:
            filters['rpt_month'] = rpt_month
        if preg_id:
            filters['preg_id'] = preg_id
        
        stats = data_service.get_classification_statistics(filters)
        total = sum(stats.values())
        
        result = {
            'total': total,
            'categories': [
                {
                    'category': category,
                    'count': count,
                    'percentage': round(count / total * 100, 1) if total > 0 else 0
                }
                for category, count in stats.items()
            ],
            'filters': filters
        }
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")


@app.get("/health")
async def health_check():
    """健康检查接口（无需鉴权）"""
    return {
        "status": "healthy",
        "service": "费用分析API",
        "timestamp": datetime.now().isoformat(),
        "auth_enabled": AuthConfig.ENABLE_AUTH
    }


@app.get("/auth/info")
async def auth_info(token: str = Depends(verify_token)):
    """获取当前token信息（需要鉴权）"""
    return {
        "message": "鉴权成功",
        "token_valid": True,
        "timestamp": datetime.now().isoformat()
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
