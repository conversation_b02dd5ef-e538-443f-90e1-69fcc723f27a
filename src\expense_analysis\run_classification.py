#!/usr/bin/env python3
"""
费用分类快速启动脚本
"""
import sys
import os

def main():
    """主函数"""
    print("=" * 60)
    print("费用分类AI工具")
    print("=" * 60)
    
    # 显示使用说明
    print("\n使用说明:")
    print("1. 发现分类: python run_classification.py discover")
    print("2. 执行分类: python run_classification.py classify") 
    print("3. 一步完成: python run_classification.py full")
    print("4. 运行测试: python run_classification.py test")
    
    if len(sys.argv) < 2:
        print("\n请选择操作模式:")
        print("  discover - 发现分类类别")
        print("  classify - 执行全量分类")
        print("  full     - 发现+分类一步完成")
        print("  test     - 运行测试")
        return
    
    mode = sys.argv[1].lower()
    
    if mode == 'test':
        # 运行测试
        print("\n开始运行测试...")
        os.system('python test_classification.py')
        
    elif mode in ['discover', 'classify', 'full']:
        # 获取参数
        table = input("请输入表名 (默认: clean_ele_payment): ").strip()
        if not table:
            table = 'clean_ele_payment'
        
        month = input("请输入筛选月份 (YYYYMM格式，如202504，留空表示不筛选): ").strip()
        city = input("请输入地市ID (留空表示不筛选): ").strip()
        
        # 构建命令
        cmd = f'python expense_classifier.py --table {table} --mode {mode}'
        
        if month:
            if len(month) == 6 and month.isdigit():
                cmd += f' --month {month}'
            else:
                print("错误：月份格式应为YYYYMM，如202504")
                return
        
        if city:
            cmd += f' --city {city}'
        
        print(f"\n执行命令: {cmd}")
        print("=" * 60)
        
        # 执行命令
        os.system(cmd)
        
    else:
        print(f"未知模式: {mode}")
        print("支持的模式: discover, classify, full, test")

if __name__ == '__main__':
    main()
