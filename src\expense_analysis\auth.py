"""
费用分析API鉴权模块
"""
from fastapi import HTTPException, Security, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
from .auth_config import AuthConfig

# 创建Bearer token验证器
security = HTTPBearer(auto_error=False)

def verify_token(credentials: Optional[HTTPAuthorizationCredentials] = Security(security)) -> str:
    """
    验证API Token

    Args:
        credentials: HTTP Bearer token (可选)

    Returns:
        str: 验证通过的token

    Raises:
        HTTPException: token无效时抛出401错误
    """
    # 如果鉴权被禁用，直接返回
    if not AuthConfig.ENABLE_AUTH:
        return "auth_disabled"

    # 如果启用鉴权但没有提供credentials
    if credentials is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要提供API Token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = credentials.credentials
    valid_tokens = AuthConfig.get_valid_tokens()

    if token not in valid_tokens:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的API Token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return token


def get_current_token(credentials: Optional[HTTPAuthorizationCredentials] = Security(security)) -> str:
    """
    获取当前请求的token (用于需要token信息的接口)
    """
    return verify_token(credentials)


# 可选：创建一个装饰器版本
def require_auth(func):
    """
    装饰器：为函数添加鉴权要求
    """
    def wrapper(*args, **kwargs):
        # 这里可以添加额外的鉴权逻辑
        return func(*args, **kwargs)
    return wrapper
