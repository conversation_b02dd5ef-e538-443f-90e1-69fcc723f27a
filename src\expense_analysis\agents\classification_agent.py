"""
费用分类代理
负责使用AI对费用备注进行智能分类
"""
import asyncio
import time
from typing import List, Dict, Tuple, Optional
import json
import re
from datetime import datetime

from ...core import LLMFactory
from ..models import ExpenseRecord, ClassificationResult, SamplingConfig
from ..services.data_service import DataService
from ..config.model_config import get_model_config

class ExpenseClassificationAgent:
    """费用分类代理"""
    
    def __init__(self, llm_provider: str = "local", ai_timeout: int = 120, model_choice: str = "balanced"):
        """
        初始化分类代理

        Args:
            llm_provider: LLM提供商 ("local", "openai", "anthropic")
            ai_timeout: AI调用超时时间（秒）
            model_choice: 模型选择 ("fastest", "balanced", "accurate", "current")
        """
        # 获取费用分析专用模型配置
        model_config = get_model_config(model_choice)

        # 使用配置中的参数，但允许外部覆盖超时时间
        final_timeout = ai_timeout if ai_timeout != 120 else model_config.get("timeout", 120)

        # 对于分类任务，使用优化配置
        self.llm = LLMFactory.create_chat_model(
            provider=llm_provider,
            model_name=model_config["model_name"],
            temperature=model_config["temperature"],
            preserve_think_tags=model_config["preserve_think_tags"],
            timeout=final_timeout
        )
        self.data_service = DataService()
        self.discovered_categories = []
        self.ai_timeout = final_timeout
        self.model_choice = model_choice
        self.model_config = model_config

        print(f"🤖 使用模型: {model_config['model_name']}")
        print(f"🌡️  温度设置: {model_config['temperature']}")
        print(f"⏱️  超时时间: {final_timeout}秒")
        
    def discover_categories_from_sample(self, table_name: str, filters: Dict = None, 
                                      config: SamplingConfig = None) -> List[str]:
        """
        从样本数据中发现分类类别
        
        Args:
            table_name: 表名
            filters: 过滤条件
            config: 采样配置
            
        Returns:
            发现的分类列表
        """
        if config is None:
            config = SamplingConfig()
        
        print(f"开始从表 {table_name} 采样数据进行分类发现...")
        
        # 1. 直接获取不同的备注内容
        remarks = self.data_service.get_stratified_sample(table_name, filters or {}, config)
        print(f"获取到 {len(remarks)} 条不同的备注内容")

        if not remarks:
            print("未获取到有效备注内容")
            return []
        
        # 3. 使用AI分析并发现分类
        categories = self._analyze_remarks_for_categories(remarks[:200])  # 限制分析数量
        
        self.discovered_categories = categories
        print(f"发现的分类类别: {categories}")
        
        return categories
    
    def _analyze_remarks_for_categories(self, remarks: List[str]) -> List[str]:
        """使用AI分析备注并发现分类"""
        
        # 构建分析提示词
        remarks_text = "\n".join([f"{i+1}. {remark}" for i, remark in enumerate(remarks[:100])])
        
        prompt = f"""
请分析以下费用备注内容，从"原因"角度总结出主要的分类类别。

备注样本（共{len(remarks)}条，显示前100条）：
{remarks_text}

分析要求：
1. 重点关注导致费用产生的原因，而不是费用类型
2. 仔细观察这些备注的共同特征和模式
3. 根据备注内容的实际情况，归纳出5-10个原因分类
4. 类别要互斥且完整，能覆盖大部分备注内容
5. 类别名称要简洁明确，便于理解
6. 按重要性和出现频率排序
7. 只返回类别名称，每行一个，不要编号和解释

请基于实际备注内容进行分析，不要使用预设的分类模板。

示例格式：
清单序号定位
数据异常处理
业务流程问题
其他原因
"""
        
        try:
            response = self.llm.invoke(prompt)
            
            # 解析响应，提取分类
            categories = []
            response_text = response.content if hasattr(response, 'content') else str(response)
            for line in response_text.strip().split('\n'):
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('*'):
                    # 清理可能的编号
                    clean_line = re.sub(r'^\d+[\.\)]\s*', '', line)
                    clean_line = re.sub(r'^[一二三四五六七八九十]+[\.\)]\s*', '', clean_line)
                    if clean_line:
                        categories.append(clean_line)
            
            return categories[:8]  # 最多8个分类
            
        except Exception as e:
            print(f"AI分析分类时出错: {e}")
            # 返回基础原因分类（AI超时时的备用）
            return [
                "无备注",
                "清单序号定位",
                "数据异常处理",
                "业务流程问题",
                "其他原因"
            ]
    
    def classify_batch_remarks(self, remarks: List[str], categories: List[str], 
                             batch_size: int = 50) -> Dict[str, ClassificationResult]:
        """
        批量分类备注
        
        Args:
            remarks: 备注列表
            categories: 分类类别列表
            batch_size: 批处理大小
            
        Returns:
            分类结果字典 {备注: 分类结果}
        """
        results = {}
        total_batches = (len(remarks) + batch_size - 1) // batch_size
        
        print(f"开始批量分类 {len(remarks)} 条备注，分 {total_batches} 批处理...")
        
        for i in range(0, len(remarks), batch_size):
            batch = remarks[i:i + batch_size]
            batch_num = i // batch_size + 1
            
            print(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch)} 条备注")
            
            try:
                batch_results = self._classify_single_batch(batch, categories)
                results.update(batch_results)
                
                # 避免API限流
                if batch_num < total_batches:
                    time.sleep(1)
                    
            except Exception as e:
                print(f"处理第 {batch_num} 批时出错: {e}")
                # 为失败的批次设置默认分类
                for remark in batch:
                    results[remark] = ClassificationResult(
                        category="其他费用",
                        confidence=0.1,
                        reason="分类失败，设为默认类别"
                    )
        
        return results
    
    def _classify_single_batch(self, remarks: List[str], categories: List[str]) -> Dict[str, ClassificationResult]:
        """分类单个批次的备注"""
        
        # 构建分类提示词
        categories_text = "\n".join([f"{i+1}. {cat}" for i, cat in enumerate(categories)])
        remarks_text = "\n".join([f"{i+1}. {remark}" for i, remark in enumerate(remarks)])
        
        prompt = f"""
请将以下费用备注逐一分类到指定的类别中。

分类类别：
{categories_text}

需要分类的备注：
{remarks_text}

请为每条备注选择最合适的分类类别，并给出置信度（0-1之间的小数）。

返回格式（JSON）：
{{
    "1": {{"category": "设备维护费", "confidence": 0.9}},
    "2": {{"category": "人工服务费", "confidence": 0.8}},
    ...
}}

注意：
1. 必须返回有效的JSON格式
2. 置信度要合理，不确定的可以给较低分数
3. 如果无法确定，归类到"其他费用"
"""
        
        try:
            response = self.llm.invoke(prompt)

            # 解析JSON响应
            response_text = response.content if hasattr(response, 'content') else str(response)
            result_json = self._extract_json_from_response(response_text)

            if not result_json:
                print(f"❌ AI响应解析失败，原始响应前500字符:")
                print(f"   {response_text[:500]}...")
                raise ValueError("无法解析AI响应为JSON")
            
            # 转换为结果字典
            results = {}
            for idx_str, classification in result_json.items():
                try:
                    idx = int(idx_str) - 1  # 转换为0基索引
                    if 0 <= idx < len(remarks):
                        remark = remarks[idx]
                        results[remark] = ClassificationResult(
                            category=classification.get('category', '其他费用'),
                            confidence=float(classification.get('confidence', 0.5)),
                            reason=classification.get('reason', '')
                        )
                except (ValueError, IndexError):
                    continue
            
            return results
            
        except Exception as e:
            print(f"分类批次时出错: {e}")
            # 返回默认分类
            results = {}
            for remark in remarks:
                results[remark] = ClassificationResult(
                    category="其他费用",
                    confidence=0.1,
                    reason=f"分类出错: {str(e)}"
                )
            return results
    
    def _extract_json_from_response(self, response_text: str) -> Optional[Dict]:
        """从AI响应中提取JSON"""
        try:
            # 尝试直接解析
            return json.loads(response_text.strip())
        except:
            pass
        
        # 尝试提取JSON代码块
        json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
        match = re.search(json_pattern, response_text, re.DOTALL)
        if match:
            try:
                return json.loads(match.group(1))
            except:
                pass
        
        # 尝试提取大括号内容
        brace_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        match = re.search(brace_pattern, response_text, re.DOTALL)
        if match:
            try:
                return json.loads(match.group(0))
            except:
                pass
        
        return None

    def classify_comprehensive_remarks(self, comprehensive_remarks: List[Dict], categories: List[str],
                                     batch_size: int = 20, max_retries: int = 3, adaptive_batch: bool = True) -> Dict[str, ClassificationResult]:
        """
        综合分析多条备注的分类 支持自适应批量大小和重试机制

        Args:
            comprehensive_remarks: 综合备注信息列表
            categories: 分类类别列表
            batch_size: 初始批处理大小
            max_retries: 最大重试次数
            adaptive_batch: 是否启用自适应批量大小

        Returns:
            分类结果字典 {record_id: 分类结果}
        """
        results = {}
        failed_batches = []  # 记录失败的批次，用于重试
        current_batch_size = batch_size

        print(f"🚀 开始综合分析 {len(comprehensive_remarks)} 条记录")
        print(f"📊 初始批量大小: {current_batch_size}, 自适应批量: {'开启' if adaptive_batch else '关闭'}")
        print(f"🔄 最大重试次数: {max_retries}")

        # 性能监控
        success_count = 0
        timeout_count = 0
        error_count = 0
        total_start_time = time.time()

        i = 0
        while i < len(comprehensive_remarks):
            batch = comprehensive_remarks[i:i + current_batch_size]
            batch_num = (i // current_batch_size) + 1

            # 计算剩余批次
            remaining_items = len(comprehensive_remarks) - i
            remaining_batches = (remaining_items + current_batch_size - 1) // current_batch_size

            print(f"📦 处理第 {batch_num} 批，包含 {len(batch)} 条记录 (批量大小: {current_batch_size})")

            batch_start_time = time.time()
            batch_success = False

            for retry in range(max_retries + 1):
                try:
                    # 使用可变的分类列表，支持动态添加新分类
                    mutable_categories = categories.copy()
                    batch_results = self._classify_comprehensive_batch(batch, mutable_categories)
                    results.update(batch_results)

                    # 如果分类列表有变化，更新原始列表
                    if len(mutable_categories) > len(categories):
                        new_categories = mutable_categories[len(categories):]
                        categories.extend(new_categories)
                        print(f"🆕 发现新分类: {', '.join(new_categories)}")

                    batch_duration = time.time() - batch_start_time
                    success_count += 1
                    batch_success = True

                    # 极限优化：更激进的自适应批量调整
                    if adaptive_batch:
                        if batch_duration < 30:  # 30秒内完成就增加
                            if current_batch_size < 100:  # 极限批量
                                current_batch_size = min(current_batch_size + 10, 100)  # 大步长增长
                                print(f"🚀 极限模式：批量增加到 {current_batch_size}")
                        elif batch_duration > 60:  # 超过60秒才减少
                            current_batch_size = max(current_batch_size - 5, 10)
                            print(f"🔽 调整批量到: {current_batch_size}")

                    print(f"✅ 批次处理成功 (用时: {batch_duration:.1f}秒)")

                    # 计算并显示进度
                    progress = (i + len(batch)) / len(comprehensive_remarks) * 100
                    elapsed = time.time() - total_start_time
                    if progress > 0:
                        estimated_total = elapsed / (progress / 100)
                        remaining = estimated_total - elapsed
                        print(f"📈 进度: {progress:.1f}% (预计剩余: {remaining/60:.1f}分钟)")

                    break

                except Exception as e:
                    error_msg = str(e)
                    if retry < max_retries:
                        wait_time = min((retry + 1) * 2, 10)  # 指数退避，最大10秒
                        if "timeout" in error_msg.lower() or "read timed out" in error_msg.lower():
                            timeout_count += 1
                            print(f"⏰ 超时重试 {retry+1}/{max_retries} (等待{wait_time}秒)")

                            # 超时时减少批量大小
                            if adaptive_batch and current_batch_size > 3:
                                old_size = current_batch_size
                                current_batch_size = max(current_batch_size - 3, 3)
                                print(f"🔽 减少批量大小: {old_size} → {current_batch_size}")
                                # 重新分割当前批次
                                batch = comprehensive_remarks[i:i + current_batch_size]
                        else:
                            error_count += 1
                            print(f"❌ 错误重试 {retry+1}/{max_retries}: {error_msg[:100]}...")

                        time.sleep(wait_time)
                    else:
                        # 最终失败，记录失败批次
                        print(f"💥 批次最终失败: {error_msg[:100]}...")
                        failed_batches.append({
                            'batch_num': batch_num,
                            'batch': batch,
                            'error': error_msg
                        })

                        # 为失败的批次设置默认分类
                        for remark_info in batch:
                            results[remark_info['record_id']] = ClassificationResult(
                                category="其他费用",
                                confidence=0.1,
                                reason=f"分类失败: {error_msg[:50]}..."
                            )
                        break

            # 移动到下一批
            i += len(batch)

            # 极限模式：移除API延迟
            # if batch_success and i < len(comprehensive_remarks):
            #     time.sleep(0.01)  # 极限模式：几乎无延迟

        # 性能统计
        total_duration = time.time() - total_start_time
        processed_count = len(comprehensive_remarks)
        print(f"\n📊 处理完成统计:")
        print(f"   ✅ 成功批次: {success_count}")
        print(f"   ⏰ 超时次数: {timeout_count}")
        print(f"   ❌ 错误次数: {error_count}")
        print(f"   💥 失败批次: {len(failed_batches)}")
        print(f"   ⏱️  总用时: {total_duration:.1f}秒")
        print(f"   📈 平均速度: {processed_count/total_duration:.1f} 条/秒")

        # 如果有失败的批次，提供重试建议
        if failed_batches:
            print(f"\n🔄 失败批次详情:")
            for failed in failed_batches:
                print(f"   批次 {failed['batch_num']}: {len(failed['batch'])} 条记录")

        return results

    def _classify_comprehensive_batch(self, batch: List[Dict], categories: List[str]) -> Dict[str, ClassificationResult]:
        """综合分析单个批次的记录 - 超高速优化版"""

        # 构建分类提示词 - 简化版本，减少token数量
        categories_text = "\n".join([f"{i+1}. {cat}" for i, cat in enumerate(categories)])

        # 构建记录信息
        records_text = ""
        for i, remark_info in enumerate(batch):
            records_text += f"\n记录{i+1} (ID: {remark_info['record_id']}):\n"

            if remark_info['details']:
                records_text += "  明细备注:\n"
                for detail in remark_info['details']:
                    records_text += f"    - {detail['dictgroup_name']} > {detail['dict_name']}: {detail['content']}\n"

            if remark_info['mandatory_note']:
                records_text += f"  必填备注: {remark_info['mandatory_note']}\n"

            records_text += f"  综合内容: {'; '.join(remark_info['combined_content'])}\n"

        # 详细的分类提示词 - 确保AI理解分类逻辑
        prompt = f"""请仔细分析每条费用备注，根据备注内容的实际含义进行分类。

可用分类类别：
{categories_text}

需要分类的记录：
{records_text}

分类要求：
1. 仔细阅读每条备注的具体内容
2. 根据备注描述的实际情况选择最合适的类别
3. 不要机械地按顺序分配，要根据内容匹配
4. 如果备注内容与现有类别都不匹配，可以归类到最相近的类别
5. 给出合理的置信度评分

返回JSON格式：
{{
    "1": {{"category": "具体类别名", "confidence": 0.9, "is_new_category": false}},
    "2": {{"category": "具体类别名", "confidence": 0.8, "is_new_category": false}},
    ...
}}

重要：必须根据备注内容的实际含义进行分类，不要按顺序分配！"""

        try:
            response = self.llm.invoke(prompt)

            # 解析JSON响应
            response_text = response.content if hasattr(response, 'content') else str(response)
            result_json = self._extract_json_from_response(response_text)

            if not result_json:
                print(f"❌ 综合分析批次时出错: 无法解析AI响应为JSON")
                print(f"   AI响应前500字符: {response_text[:500]}...")
                raise ValueError("无法解析AI响应为JSON")

            # 转换为结果字典，并收集新分类
            results = {}
            new_categories = set()

            for idx_str, classification in result_json.items():
                try:
                    idx = int(idx_str) - 1  # 转换为0基索引
                    if 0 <= idx < len(batch):
                        record_id = batch[idx]['record_id']
                        category = classification.get('category', '其他费用')
                        is_new = classification.get('is_new_category', False)

                        # 收集新分类
                        if is_new and category not in categories:
                            new_categories.add(category)
                            print(f"🆕 发现新分类: {category}")

                        results[record_id] = ClassificationResult(
                            category=category,
                            confidence=float(classification.get('confidence', 0.5)),
                            reason=classification.get('reason', '')
                        )
                except (ValueError, IndexError):
                    continue

            # 如果有新分类，更新分类列表
            if new_categories:
                categories.extend(list(new_categories))
                print(f"✅ 已添加 {len(new_categories)} 个新分类到分类列表")

            return results

        except Exception as e:
            error_msg = str(e)
            print(f"综合分析批次时出错: {error_msg}")

            # 检查是否是可重试的网络错误
            retryable_errors = [
                "429",  # Too Many Requests
                "502",  # Bad Gateway
                "503",  # Service Unavailable
                "504",  # Gateway Timeout
                "500",  # Internal Server Error
                "Too Many Requests",
                "Bad Gateway",
                "Service Unavailable",
                "Gateway Timeout",
                "Internal Server Error",
                "Connection",  # 连接错误
                "Timeout",     # 超时错误
                "Network"      # 网络错误
            ]

            # 检查是否是可重试的错误
            is_retryable = any(err in error_msg for err in retryable_errors)

            if is_retryable:
                print(f"❌ 网络/服务错误，需要重试: {error_msg[:100]}...")
                raise e  # 重新抛出异常，让上层处理重试

            # 其他错误返回默认分类
            print(f"⚠️ 使用默认分类处理批次")
            results = {}
            for remark_info in batch:
                results[remark_info['record_id']] = ClassificationResult(
                    category="其他费用",
                    confidence=0.1,
                    reason=f"分类出错: {error_msg[:50]}"
                )
            return results
