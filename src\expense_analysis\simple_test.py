"""
简单测试脚本 - 测试采样功能
"""
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def test_simple_sampling():
    """测试简单采样"""
    try:
        from src.expense_analysis.services.data_service import DataService
        from src.expense_analysis.models import SamplingConfig
        
        print("开始测试简单采样...")
        
        data_service = DataService()
        
        # 测试简单查询
        with data_service.get_connection() as conn:
            cursor = conn.cursor()
            
            # 简单查询，不使用分层采样
            sql = """
            SELECT billaccountpaymentdetail_id, payment_code, paymentdetail_note, 
                   mandatory_note, billamount_startdate, billamount_enddate
            FROM clean_ele_payment 
            WHERE paymentdetail_note IS NOT NULL 
            LIMIT 10
            """
            
            print("执行SQL查询...")
            cursor.execute(sql)
            results = cursor.fetchall()
            
            print(f"✅ 获取到 {len(results)} 条数据")
            
            # 显示前3条
            for i, row in enumerate(results[:3], 1):
                print(f"\n记录 {i}:")
                print(f"  ID: {row[0]}")
                print(f"  编码: {row[1]}")
                print(f"  备注: {row[2][:100] if row[2] else 'None'}...")
                print(f"  必填备注: {row[3][:50] if row[3] else 'None'}...")
                print(f"  开始日期: {row[4]}")
                print(f"  结束日期: {row[5]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_date_format():
    """测试日期格式转换"""
    try:
        from src.expense_analysis.services.data_service import DataService
        
        print("\n测试日期格式转换...")
        
        data_service = DataService()
        
        with data_service.get_connection() as conn:
            cursor = conn.cursor()
            
            # 测试DATE_FORMAT函数
            sql = """
            SELECT billamount_startdate, 
                   DATE_FORMAT(billamount_startdate, '%Y%m') as rpt_month
            FROM clean_ele_payment 
            WHERE billamount_startdate IS NOT NULL 
            LIMIT 5
            """
            
            print("测试DATE_FORMAT函数...")
            cursor.execute(sql)
            results = cursor.fetchall()
            
            print(f"✅ 日期格式转换成功，获取 {len(results)} 条结果")
            
            for row in results:
                print(f"  原始日期: {row[0]} → 转换后: {row[1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日期格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("简单采样测试")
    print("=" * 50)
    
    tests = [
        ("简单查询测试", test_simple_sampling),
        ("日期格式测试", test_date_format),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        print(f"测试: {test_name}")
        print(f"{'-' * 30}")
        
        if test_func():
            passed += 1
        else:
            print(f"测试 {test_name} 失败")
    
    print(f"\n{'=' * 50}")
    print(f"测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 基础查询功能正常！")
    else:
        print("❌ 部分测试失败")

if __name__ == '__main__':
    main()
