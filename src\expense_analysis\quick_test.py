"""
快速测试脚本
"""
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def test_table_connection():
    """测试表连接"""
    try:
        from src.expense_analysis.services.data_service import DataService
        data_service = DataService()
        
        # 测试原表
        count = data_service.get_table_count("clean_ele_payment")
        print(f"✅ 原表 clean_ele_payment: {count:,} 条记录")
        
        # 测试分类结果表
        try:
            data_service._ensure_classification_table()
            print("✅ 分类结果表 ele_payment_ai_sort 已存在")
        except Exception as e:
            print(f"❌ 分类结果表检查失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_sample_data():
    """测试获取样本数据"""
    try:
        from src.expense_analysis.services.data_service import DataService
        data_service = DataService()
        
        # 简单查询几条数据
        import pymysql
        with data_service.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            sql = """
            SELECT billaccountpaymentdetail_id, payment_code, paymentdetail_note, mandatory_note
            FROM clean_ele_payment 
            WHERE paymentdetail_note IS NOT NULL 
            LIMIT 3
            """
            
            cursor.execute(sql)
            results = cursor.fetchall()
            
            print(f"✅ 获取到 {len(results)} 条样本数据")
            
            for i, row in enumerate(results, 1):
                print(f"\n样本 {i}:")
                print(f"  ID: {row['billaccountpaymentdetail_id']}")
                print(f"  编码: {row['payment_code']}")
                print(f"  备注: {row['paymentdetail_note'][:100] if row['paymentdetail_note'] else 'None'}...")
                print(f"  必填备注: {row['mandatory_note'][:50] if row['mandatory_note'] else 'None'}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 样本数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("费用分类快速测试")
    print("=" * 50)
    
    tests = [
        ("表连接测试", test_table_connection),
        ("样本数据测试", test_sample_data),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        print(f"测试: {test_name}")
        print(f"{'-' * 30}")
        
        if test_func():
            passed += 1
        else:
            print(f"测试 {test_name} 失败")
    
    print(f"\n{'=' * 50}")
    print(f"测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 基础功能正常！")
        print("\n下一步:")
        print("1. 确保已创建表 ele_payment_ai_sort")
        print("2. 运行: python src/expense_analysis/expense_classifier.py --table clean_ele_payment --mode discover --sample-size 50")
    else:
        print("❌ 部分测试失败")

if __name__ == '__main__':
    main()
