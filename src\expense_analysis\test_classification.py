"""
费用分类功能测试脚本
"""
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.expense_analysis.expense_classifier import ExpenseClassifier
from src.expense_analysis.models import SamplingConfig

def test_database_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    
    try:
        from src.expense_analysis.services.data_service import DataService
        data_service = DataService()
        
        with data_service.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
        print("✅ 数据库连接成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_table_access():
    """测试表访问"""
    print("测试表访问...")
    
    try:
        from src.expense_analysis.services.data_service import DataService
        data_service = DataService()
        
        table_name = "clean_ele_payment"
        count = data_service.get_table_count(table_name)
        print(f"✅ 表 {table_name} 访问成功，共有 {count:,} 条记录")
        return True
        
    except Exception as e:
        print(f"❌ 表访问失败: {e}")
        return False

def test_sampling():
    """测试采样功能"""
    print("测试采样功能...")
    
    try:
        from src.expense_analysis.services.data_service import DataService
        data_service = DataService()
        
        table_name = "clean_ele_payment"
        config = SamplingConfig(sample_size=5)  # 很小的样本测试
        
        samples = data_service.get_stratified_sample(table_name, {}, config)
        print(f"✅ 采样成功，获取到 {len(samples)} 条样本")
        
        if samples:
            sample = samples[0]
            print(f"样本示例: ID={sample.billaccountpaymentdetail_id}, 地市={sample.preg_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 采样失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("费用分类功能测试")
    print("=" * 60)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("表访问", test_table_access),
        ("采样功能", test_sampling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"测试: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试 {test_name} 失败")
        except Exception as e:
            print(f"测试 {test_name} 异常: {e}")
    
    print(f"\n{'=' * 60}")
    print(f"测试结果: {passed}/{total} 通过")
    print(f"{'=' * 60}")
    
    if passed == total:
        print("🎉 所有测试通过！可以开始使用费用分类功能。")
        print("\n使用方法:")
        print("python expense_classifier.py --table clean_ele_payment --mode discover")
    else:
        print("❌ 部分测试失败，请检查配置和环境。")

if __name__ == '__main__':
    main()
