"""
费用分类分析API
为Dify提供数据查询接口
"""

from flask import Flask, request, jsonify, send_file
import mysql.connector
import pandas as pd
import io
import json
from datetime import datetime
import os

app = Flask(__name__)

class AnalysisAPI:
    def __init__(self):
        self.db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'user': os.getenv('DB_USER', 'root'),
            'password': os.getenv('DB_PASSWORD', ''),
            'database': os.getenv('DB_NAME', 'your_database'),
            'charset': 'utf8mb4'
        }
    
    def get_connection(self):
        return mysql.connector.connect(**self.db_config)
    
    def get_category_statistics(self, month):
        """获取分类统计数据"""
        sql = """
        SELECT 
            ai_category as category,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ele_payment_ai_sort WHERE rpt_month = %s), 1) as percentage,
            (SELECT original_remark 
             FROM ele_payment_ai_sort s2 
             WHERE s2.ai_category = s1.ai_category 
               AND s2.rpt_month = %s
               AND s2.original_remark IS NOT NULL 
               AND s2.original_remark != ''
             GROUP BY original_remark 
             ORDER BY COUNT(*) DESC 
             LIMIT 1) as typical_remark,
            ROUND(AVG(ai_confidence), 3) as avg_confidence
        FROM ele_payment_ai_sort s1
        WHERE rpt_month = %s
        GROUP BY ai_category
        ORDER BY COUNT(*) DESC
        """
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(sql, (month, month, month))
            results = cursor.fetchall()
            
            return [
                {
                    'category': row[0],
                    'count': row[1], 
                    'percentage': row[2],
                    'typical_remark': row[3] or '无典型备注',
                    'avg_confidence': row[4]
                }
                for row in results
            ]
    
    def get_category_details(self, month, category, limit=100):
        """获取指定分类的明细数据"""
        sql = """
        SELECT 
            billaccountpaymentdetail_id,
            payment_code,
            preg_name,
            reg_name,
            original_remark,
            ai_confidence,
            ai_classified_time
        FROM ele_payment_ai_sort
        WHERE rpt_month = %s AND ai_category = %s
        ORDER BY ai_classified_time DESC
        LIMIT %s
        """
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(sql, (month, category, limit))
            results = cursor.fetchall()
            
            return [
                {
                    'record_id': row[0],
                    'payment_code': row[1],
                    'province': row[2],
                    'city': row[3],
                    'remark': row[4],
                    'confidence': row[5],
                    'classified_time': row[6].strftime('%Y-%m-%d %H:%M:%S') if row[6] else None
                }
                for row in results
            ]
    
    def get_overall_summary(self, month):
        """获取总体概况"""
        sql = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT ai_category) as category_count,
            ROUND(AVG(ai_confidence), 3) as avg_confidence,
            COUNT(CASE WHEN ai_confidence >= 0.8 THEN 1 END) as high_confidence_count,
            MIN(ai_classified_time) as first_classified,
            MAX(ai_classified_time) as last_classified
        FROM ele_payment_ai_sort
        WHERE rpt_month = %s
        """
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(sql, (month,))
            result = cursor.fetchone()
            
            return {
                'total_records': result[0],
                'category_count': result[1],
                'avg_confidence': result[2],
                'high_confidence_count': result[3],
                'high_confidence_rate': round(result[3] / result[0] * 100, 1) if result[0] > 0 else 0,
                'first_classified': result[4].strftime('%Y-%m-%d %H:%M:%S') if result[4] else None,
                'last_classified': result[5].strftime('%Y-%m-%d %H:%M:%S') if result[5] else None
            }

# 创建API实例
api = AnalysisAPI()

@app.route('/api/analysis/summary/<month>', methods=['GET'])
def get_summary(month):
    """获取分析概要"""
    try:
        summary = api.get_overall_summary(month)
        statistics = api.get_category_statistics(month)
        
        return jsonify({
            'success': True,
            'data': {
                'summary': summary,
                'statistics': statistics
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/category/<month>/<category>', methods=['GET'])
def get_category_detail(month, category):
    """获取分类明细"""
    try:
        limit = request.args.get('limit', 100, type=int)
        details = api.get_category_details(month, category, limit)
        
        return jsonify({
            'success': True,
            'data': {
                'category': category,
                'month': month,
                'details': details,
                'count': len(details)
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/export/<month>/<category>', methods=['GET'])
def export_category_excel(month, category):
    """导出分类明细为Excel"""
    try:
        details = api.get_category_details(month, category, limit=10000)
        
        # 创建DataFrame
        df = pd.DataFrame(details)
        if not df.empty:
            df.columns = ['记录ID', '支付代码', '省市', '地市', '备注内容', '置信度', '分类时间']
        
        # 生成Excel
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=f'{category}明细', index=False)
        
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'{month}_{category}_明细.xlsx'
        )
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/report/<month>', methods=['POST'])
def generate_report(month):
    """生成分析报告（供Dify调用）"""
    try:
        summary = api.get_overall_summary(month)
        statistics = api.get_category_statistics(month)
        
        # 构建报告数据
        report_data = {
            'month': month,
            'summary': summary,
            'statistics': statistics,
            'generated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return jsonify({
            'success': True,
            'data': report_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
