#!/usr/bin/env python3
"""
API频率限制测试工具
用于分析429错误的根本原因，测试不同的并发策略和请求频率
"""

import time
import threading
import requests
import json
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
import argparse
from datetime import datetime

class APILimitTester:
    def __init__(self):
        self.api_url = "http://ai.ai.iot.chinamobile.com/imaas/v1/chat/completions"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer sk-bCzKwiWgIHHKhTDxxi8GcVT46XKcXz7aqqaP2aXOVLYjhbZZ"  # 使用实际的API密钥
        }
        self.test_results = []
        self.lock = threading.Lock()
        
    def create_test_payload(self, test_id: int, batch_size: int = 1) -> Dict[str, Any]:
        """创建测试用的API请求负载"""
        if batch_size == 1:
            # 单条记录测试
            content = f"测试请求 {test_id}: 请分类这个费用: 办公用品采购"
        else:
            # 批量记录测试 - 模拟实际的批次大小
            records = []
            for i in range(batch_size):
                records.append(f"记录{i+1} (ID: test_{test_id}_{i+1}):\n  备注: 办公用品采购_{i+1}")
            content = f"批次 {test_id} (共{batch_size}条记录):\n" + "\n".join(records)

        return {
            "model": "Qwen3-235B-A22B",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个费用分类专家。请将以下费用备注分类到合适的类别中。"
                },
                {
                    "role": "user",
                    "content": content
                }
            ],
            "temperature": 0.1,
            "max_tokens": 2000  # 增加token数量以处理批量数据
        }
    
    def single_request(self, test_id: int, delay: float = 0, batch_size: int = 1) -> Dict[str, Any]:
        """发送单个API请求"""
        if delay > 0:
            time.sleep(delay)
            
        start_time = time.time()
        
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=self.create_test_payload(test_id, batch_size),
                timeout=30
            )
            
            duration = time.time() - start_time
            
            result = {
                'test_id': test_id,
                'thread_id': threading.current_thread().ident,
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'duration': duration,
                'timestamp': datetime.now().isoformat(),
                'error': None
            }
            
            if response.status_code != 200:
                result['error'] = f"{response.status_code} {response.reason}"
                
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            return {
                'test_id': test_id,
                'thread_id': threading.current_thread().ident,
                'status_code': 0,
                'success': False,
                'duration': duration,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def test_sequential_requests(self, count: int, delay: float) -> List[Dict[str, Any]]:
        """测试顺序请求"""
        print(f"🔄 测试顺序请求: {count}个请求, 间隔{delay}秒")
        results = []
        
        for i in range(count):
            result = self.single_request(i + 1, delay)
            results.append(result)
            
            status = "✅" if result['success'] else "❌"
            print(f"{status} 请求{i+1}: {result['status_code']} (用时: {result['duration']:.2f}s)")
            
        return results
    
    def test_concurrent_requests(self, count: int, max_workers: int, delay: float = 0) -> List[Dict[str, Any]]:
        """测试并发请求"""
        print(f"🚀 测试并发请求: {count}个请求, {max_workers}个线程, 间隔{delay}秒")
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = []
            for i in range(count):
                future = executor.submit(self.single_request, i + 1, delay)
                futures.append(future)
                
                # 如果有延迟，在提交任务之间等待
                if delay > 0 and i < count - 1:
                    time.sleep(delay)
            
            # 收集结果
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
                
                status = "✅" if result['success'] else "❌"
                print(f"{status} 线程{result['thread_id']} 请求{result['test_id']}: {result['status_code']} (用时: {result['duration']:.2f}s)")
        
        return results
    
    def test_burst_requests(self, burst_size: int, burst_count: int, burst_interval: float) -> List[Dict[str, Any]]:
        """测试突发请求模式"""
        print(f"💥 测试突发请求: {burst_count}个突发, 每个突发{burst_size}个请求, 间隔{burst_interval}秒")
        all_results = []
        
        for burst in range(burst_count):
            print(f"🔥 突发 {burst + 1}/{burst_count}")
            
            # 同时发送一批请求
            with ThreadPoolExecutor(max_workers=burst_size) as executor:
                futures = [executor.submit(self.single_request, burst * burst_size + i + 1) 
                          for i in range(burst_size)]
                
                burst_results = [future.result() for future in as_completed(futures)]
                all_results.extend(burst_results)
                
                # 显示这个突发的结果
                success_count = sum(1 for r in burst_results if r['success'])
                print(f"   突发{burst + 1}结果: {success_count}/{burst_size} 成功")
            
            # 突发之间的间隔
            if burst < burst_count - 1:
                print(f"   等待 {burst_interval} 秒...")
                time.sleep(burst_interval)
        
        return all_results

    def test_realistic_workload(self, threads: int, batch_size: int, total_batches: int, delay: float = 0) -> List[Dict[str, Any]]:
        """测试真实工作负载 - 模拟实际的批次大小和并发"""
        print(f"🎯 测试真实工作负载: {threads}线程, 每批{batch_size}条记录, 共{total_batches}批次, 间隔{delay}秒")
        results = []

        with ThreadPoolExecutor(max_workers=threads) as executor:
            # 提交所有任务
            futures = []
            for i in range(total_batches):
                future = executor.submit(self.single_request, i + 1, delay, batch_size)
                futures.append(future)

                # 如果有延迟，在提交任务之间等待
                if delay > 0 and i < total_batches - 1:
                    time.sleep(delay)

            # 收集结果
            for future in as_completed(futures):
                result = future.result()
                results.append(result)

                status = "✅" if result['success'] else "❌"
                print(f"{status} 线程{result['thread_id']} 批次{result['test_id']} ({batch_size}条): {result['status_code']} (用时: {result['duration']:.2f}s)")

        return results
    
    def analyze_results(self, results: List[Dict[str, Any]], test_name: str):
        """分析测试结果"""
        print(f"\n📊 {test_name} 结果分析:")
        print("=" * 50)
        
        total = len(results)
        success = sum(1 for r in results if r['success'])
        failed = total - success
        
        print(f"总请求数: {total}")
        print(f"成功: {success} ({success/total*100:.1f}%)")
        print(f"失败: {failed} ({failed/total*100:.1f}%)")
        
        if failed > 0:
            # 分析失败原因
            error_counts = {}
            for r in results:
                if not r['success']:
                    error = r['error'] or f"HTTP {r['status_code']}"
                    error_counts[error] = error_counts.get(error, 0) + 1
            
            print("\n❌ 失败原因分析:")
            for error, count in error_counts.items():
                print(f"   {error}: {count} 次")
        
        # 性能分析
        durations = [r['duration'] for r in results if r['success']]
        if durations:
            avg_duration = sum(durations) / len(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            
            print(f"\n⏱️ 性能分析 (成功请求):")
            print(f"   平均响应时间: {avg_duration:.2f}s")
            print(f"   最快响应: {min_duration:.2f}s")
            print(f"   最慢响应: {max_duration:.2f}s")
        
        # 429错误特别分析
        error_429_count = sum(1 for r in results if '429' in str(r.get('error', '')))
        if error_429_count > 0:
            print(f"\n🚨 429错误分析:")
            print(f"   429错误次数: {error_429_count}")
            print(f"   429错误率: {error_429_count/total*100:.1f}%")
            
            # 找出第一个429错误出现的时间
            first_429 = None
            for r in results:
                if '429' in str(r.get('error', '')):
                    first_429 = r
                    break
            
            if first_429:
                print(f"   首次429错误: 请求{first_429['test_id']} (线程{first_429['thread_id']})")
        
        print("=" * 50)
        return {
            'total': total,
            'success': success,
            'failed': failed,
            'success_rate': success/total*100,
            'error_429_count': error_429_count,
            'avg_duration': sum(durations)/len(durations) if durations else 0
        }

def main():
    parser = argparse.ArgumentParser(description='API频率限制测试工具')
    parser.add_argument('--test', choices=['sequential', 'concurrent', 'burst', 'all'], 
                       default='all', help='测试类型')
    parser.add_argument('--count', type=int, default=20, help='请求数量')
    parser.add_argument('--threads', type=int, default=4, help='并发线程数')
    parser.add_argument('--delay', type=float, default=1.0, help='请求间隔(秒)')
    
    args = parser.parse_args()
    
    tester = APILimitTester()
    
    print("🧪 API频率限制测试开始")
    print(f"🎯 目标API: {tester.api_url}")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    if args.test in ['sequential', 'all']:
        # 测试1: 顺序请求，不同间隔
        for delay in [0, 0.5, 1.0, 2.0]:
            results = tester.test_sequential_requests(10, delay)
            tester.analyze_results(results, f"顺序请求(间隔{delay}s)")
            print()
    
    if args.test in ['concurrent', 'all']:
        # 测试2: 并发请求，不同线程数
        for threads in [1, 2, 4, 6, 8]:
            results = tester.test_concurrent_requests(20, threads, 0)
            tester.analyze_results(results, f"并发请求({threads}线程)")
            print()
    
    if args.test in ['burst', 'all']:
        # 测试3: 突发请求模式
        results = tester.test_burst_requests(5, 4, 3.0)
        tester.analyze_results(results, "突发请求模式")
        print()
    
    print("🎉 测试完成!")

if __name__ == "__main__":
    main()
